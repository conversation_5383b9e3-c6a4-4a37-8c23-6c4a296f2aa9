'use client'

import {
  Search, Download, Clock, User, Package, DollarSign,
  Eye, Trash2, Archive, RefreshCw,
  TrendingUp, BarChart3, FileText, Settings, CheckSquare,
  Square, X, ChevronDown, ChevronRight, AlertCircle,
  Activity, Zap, Shield, Database, Bell, Star,
  Wifi, WifiOff, Timer, Hash, Calendar, Filter, Sheet
} from 'lucide-react'
import { useState, useMemo, useEffect, useCallback } from 'react'
import * as XLSX from 'xlsx'

import { useToast } from '@/components'
import type { Product, CustomerDebt, CustomerPayment } from '@/types'

interface HistoryItem {
  id: string
  type: 'product' | 'debt' | 'payment' | 'login' | 'system' | 'security' | 'backup' | 'notification'
  action: string
  description: string
  user: string
  timestamp: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'success' | 'warning' | 'error' | 'info'
  category: string
  ipAddress?: string
  userAgent?: string
  details?: Record<string, unknown>
  tags?: string[]
}

interface FilterOptions {
  types: string[]
  priorities: string[]
  statuses: string[]
  dateRange: string
  users: string[]
  categories: string[]
}

export default function History() {
  const { addToast } = useToast()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [dateRange, setDateRange] = useState('7days')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [selectedActivity, setSelectedActivity] = useState<HistoryItem | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'list' | 'timeline' | 'grid'>('list')
  const [sortBy, setSortBy] = useState<'timestamp' | 'type' | 'priority'>('timestamp')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [realHistoryData, setRealHistoryData] = useState<HistoryItem[]>([])
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState(new Date())
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline'>('online')
  const [downloadRange, setDownloadRange] = useState<'day' | 'week' | 'month' | 'year'>('week')
  const [showDownloadOptions, setShowDownloadOptions] = useState(false)

  const [filters, setFilters] = useState<FilterOptions>({
    types: [],
    priorities: [],
    statuses: [],
    dateRange: '7days',
    users: [],
    categories: []
  })

  // Fetch real history data from APIs
  const fetchRealHistoryData = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      console.log('🔄 History: Fetching real data from APIs...')

      // Fetch all data in parallel
      const [productsRes, debtsRes, paymentsRes] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/debts'),
        fetch('/api/payments')
      ])

      // Check for API errors
      if (!productsRes.ok) throw new Error(`Products API error: ${productsRes.status}`)
      if (!debtsRes.ok) throw new Error(`Debts API error: ${debtsRes.status}`)
      if (!paymentsRes.ok) throw new Error(`Payments API error: ${paymentsRes.status}`)

      // Parse responses
      const [productsData, debtsData, paymentsData] = await Promise.all([
        productsRes.json(),
        debtsRes.json(),
        paymentsRes.json()
      ])

      console.log('✅ History: Real data fetched successfully', {
        products: productsData.data?.products?.length || 0,
        debts: debtsData.data?.debts?.length || 0,
        payments: paymentsData.data?.payments?.length || 0
      })

      // Generate history items from real data
      const historyItems = generateHistoryFromRealData({
        products: productsData.data?.products || [],
        debts: debtsData.data?.debts || [],
        payments: paymentsData.data?.payments || []
      })

      setRealHistoryData(historyItems)
      setLastUpdated(new Date())

    } catch (error) {
      console.error('❌ History: Error fetching real data:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch history data')

      // Fallback to mock data if real data fails
      setRealHistoryData(getMockHistoryData())
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Generate history items from real database data
  const generateHistoryFromRealData = useCallback((data: {
    products: Product[]
    debts: CustomerDebt[]
    payments: CustomerPayment[]
  }) => {
    const historyItems: HistoryItem[] = []

    // Generate history from products (recent additions/updates)
    data.products.slice(0, 10).forEach((product) => {
      historyItems.push({
        id: `product-${product.id}`,
        type: 'product',
        action: 'Product Added',
        description: `Added "${product.name}" to inventory`,
        user: 'Admin',
        timestamp: product.created_at,
        priority: 'medium',
        status: 'success',
        category: 'Inventory Management',
        tags: ['product', 'inventory', 'add'],
        details: {
          productName: product.name,
          price: product.price,
          retailPrice: product.retail_price,
          quantity: product.stock_quantity,
          category: product.category,
          netWeight: product.net_weight
        }
      })
    })

    // Generate history from debts (recent debt records)
    data.debts.slice(0, 15).forEach((debt) => {
      historyItems.push({
        id: `debt-${debt.id}`,
        type: 'debt',
        action: 'Debt Recorded',
        description: `New debt record for ${debt.customer_name} ${debt.customer_family_name}`,
        user: 'Admin',
        timestamp: debt.created_at,
        priority: debt.total_amount > 1000 ? 'high' : 'medium',
        status: debt.total_amount > 5000 ? 'warning' : 'info',
        category: 'Customer Management',
        tags: ['debt', 'customer', 'record'],
        details: {
          customer: `${debt.customer_name} ${debt.customer_family_name}`,
          product: debt.product_name,
          amount: debt.total_amount,
          quantity: debt.quantity,
          debtDate: debt.debt_date,
          notes: debt.notes
        }
      })
    })

    // Generate history from payments (recent payments)
    data.payments.slice(0, 15).forEach((payment) => {
      historyItems.push({
        id: `payment-${payment.id}`,
        type: 'payment',
        action: 'Payment Received',
        description: `Payment of ₱${payment.payment_amount} from ${payment.customer_name} ${payment.customer_family_name}`,
        user: payment.responsible_family_member || 'Admin',
        timestamp: payment.created_at,
        priority: payment.payment_amount > 1000 ? 'high' : 'medium',
        status: 'success',
        category: 'Payment Management',
        tags: ['payment', 'customer', 'income'],
        details: {
          customer: `${payment.customer_name} ${payment.customer_family_name}`,
          amount: payment.payment_amount,
          paymentMethod: payment.payment_method,
          paymentDate: payment.payment_date,
          responsibleMember: payment.responsible_family_member,
          notes: payment.notes
        }
      })
    })

    // Sort by timestamp (newest first)
    return historyItems.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }, [])

  // Fallback mock data function
  const getMockHistoryData = useCallback((): HistoryItem[] => [
    {
      id: 'mock-1',
      type: 'system',
      action: 'System Started',
      description: 'Using fallback data due to network issues',
      user: 'System',
      timestamp: new Date().toISOString(),
      priority: 'low',
      status: 'info',
      category: 'System Activity',
      tags: ['system', 'fallback']
    }
  ], [])

  // Use real data if available, otherwise use mock data
  const historyData: HistoryItem[] = useMemo(() => {
    return realHistoryData.length > 0 ? realHistoryData : getMockHistoryData()
  }, [realHistoryData, getMockHistoryData])

  // Filter data by date range for download
  const getFilteredDataByRange = useCallback((range: 'day' | 'week' | 'month' | 'year') => {
    const now = new Date()
    const startDate = new Date()

    switch (range) {
      case 'day':
        startDate.setDate(now.getDate() - 1)
        break
      case 'week':
        startDate.setDate(now.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(now.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }

    return historyData.filter(item => {
      const itemDate = new Date(item.timestamp)
      return itemDate >= startDate && itemDate <= now
    })
  }, [historyData])

  // Download functions
  const downloadAsCSV = useCallback((data: HistoryItem[], filename: string) => {
    // Add header with metadata
    const metadata = [
      `# Tindahan Store Activity History Export`,
      `# Generated on: ${new Date().toLocaleString('en-PH')}`,
      `# Time Range: ${downloadRange}`,
      `# Total Records: ${data.length}`,
      `# Store: Revantad Store`,
      ``,
    ].join('\n')

    const headers = ['Date', 'Time', 'Type', 'Action', 'Description', 'User', 'Priority', 'Status', 'Category', 'Tags']
    const csvContent = [
      metadata,
      headers.join(','),
      ...data.map(item => {
        const date = new Date(item.timestamp)
        return [
          date.toLocaleDateString('en-PH'),
          date.toLocaleTimeString('en-PH'),
          item.type,
          `"${item.action}"`,
          `"${item.description}"`,
          item.user,
          item.priority,
          item.status,
          `"${item.category}"`,
          `"${item.tags?.join('; ') || ''}"`
        ].join(',')
      })
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [downloadRange])

  const downloadAsJSON = useCallback((data: HistoryItem[], filename: string) => {
    const exportData = {
      metadata: {
        storeName: 'Revantad Store',
        exportDate: new Date().toISOString(),
        timeRange: downloadRange,
        totalRecords: data.length,
        generatedBy: 'Tindahan Store Management System',
        version: '1.0.0'
      },
      activities: data
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [downloadRange])

  const downloadAsExcel = useCallback((data: HistoryItem[], filename: string) => {
    // Create workbook
    const workbook = XLSX.utils.book_new()

    // Prepare data for Excel
    const excelData = data.map(item => {
      const date = new Date(item.timestamp)
      return {
        'Date': date.toLocaleDateString('en-PH'),
        'Time': date.toLocaleTimeString('en-PH'),
        'Type': item.type,
        'Action': item.action,
        'Description': item.description,
        'User': item.user,
        'Priority': item.priority,
        'Status': item.status,
        'Category': item.category,
        'Tags': item.tags?.join('; ') || ''
      }
    })

    // Create worksheet from data
    const worksheet = XLSX.utils.json_to_sheet(excelData)

    // Set column widths for better readability
    const columnWidths = [
      { wch: 12 }, // Date
      { wch: 10 }, // Time
      { wch: 10 }, // Type
      { wch: 20 }, // Action
      { wch: 40 }, // Description
      { wch: 12 }, // User
      { wch: 10 }, // Priority
      { wch: 10 }, // Status
      { wch: 20 }, // Category
      { wch: 30 }  // Tags
    ]
    worksheet['!cols'] = columnWidths

    // Add metadata sheet
    const metadataSheet = XLSX.utils.json_to_sheet([
      { Property: 'Store Name', Value: 'Revantad Store' },
      { Property: 'Export Date', Value: new Date().toLocaleString('en-PH') },
      { Property: 'Time Range', Value: downloadRange },
      { Property: 'Total Records', Value: data.length },
      { Property: 'Generated By', Value: 'Tindahan Store Management System' },
      { Property: 'Version', Value: '1.0.0' }
    ])

    // Add sheets to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Activity History')
    XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Export Info')

    // Generate Excel file and download
    XLSX.writeFile(workbook, filename)
  }, [downloadRange])

  const handleDownload = useCallback((format: 'csv' | 'json' | 'excel') => {
    try {
      const filteredData = getFilteredDataByRange(downloadRange)
      const timestamp = new Date().toISOString().split('T')[0]
      const extension = format === 'excel' ? 'xlsx' : format
      const filename = `tindahan-history-${downloadRange}-${timestamp}.${extension}`

      if (filteredData.length === 0) {
        addToast({
          type: 'warning',
          message: `No activities found for the selected time range (${downloadRange})`,
          duration: 4000
        })
        return
      }

      if (format === 'csv') {
        downloadAsCSV(filteredData, filename)
      } else if (format === 'json') {
        downloadAsJSON(filteredData, filename)
      } else if (format === 'excel') {
        downloadAsExcel(filteredData, filename)
      }

      const formatName = format === 'excel' ? 'Excel' : format.toUpperCase()
      addToast({
        type: 'success',
        message: `Successfully downloaded ${filteredData.length} activities as ${formatName}`,
        duration: 4000
      })

      setShowDownloadOptions(false)
    } catch (error) {
      console.error('Download error:', error)
      addToast({
        type: 'error',
        message: 'Failed to download activities. Please try again.',
        duration: 4000
      })
    }
  }, [downloadRange, getFilteredDataByRange, downloadAsCSV, downloadAsJSON, downloadAsExcel, addToast])

  // Fetch data on component mount
  useEffect(() => {
    console.log('🔄 History component mounted - fetching real data...')
    fetchRealHistoryData()
  }, [fetchRealHistoryData])

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        console.log('🔄 History: Auto-refreshing data...')
        fetchRealHistoryData()
      }, 30000) // Refresh every 30 seconds

      return () => clearInterval(interval)
    }
  }, [autoRefresh, fetchRealHistoryData])

  // Close download options when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (showDownloadOptions && !target.closest('.download-options-container')) {
        setShowDownloadOptions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showDownloadOptions])

  // Enhanced history data with more comprehensive information (FALLBACK - will be replaced by real data)
  const fallbackHistoryData: HistoryItem[] = useMemo(() => [
    {
      id: '1',
      type: 'product',
      action: 'Product Added',
      description: 'Added "Lucky Me Pancit Canton" to product list',
      user: 'Admin',
      timestamp: '2024-01-20T10:30:00Z',
      priority: 'medium',
      status: 'success',
      category: 'Inventory Management',
      tags: ['product', 'inventory', 'add'],
      details: {
        productName: 'Lucky Me Pancit Canton',
        price: 15.00,
        sku: 'LMC001',
        quantity: 50,
        supplier: 'Lucky Me Foods'
      }
    },
    {
      id: '2',
      type: 'debt',
      action: 'Debt Recorded',
      description: 'New debt record for Juan Dela Cruz',
      user: 'Admin',
      timestamp: '2024-01-20T09:15:00Z',
      priority: 'high',
      status: 'warning',
      category: 'Customer Management',
      tags: ['debt', 'customer', 'record'],
      details: {
        customer: 'Juan Dela Cruz',
        amount: 45.00,
        dueDate: '2024-02-20',
        contactNumber: '09123456789',
        address: 'Barangay San Jose, Cebu City'
      }
    },
    {
      id: '3',
      type: 'payment',
      action: 'Payment Received',
      description: 'Payment received from Maria Santos',
      user: 'Admin',
      timestamp: '2024-01-19T16:45:00Z',
      priority: 'medium',
      status: 'success',
      category: 'Financial Transaction',
      tags: ['payment', 'customer', 'income'],
      details: {
        customer: 'Maria Santos',
        amount: 120.00,
        paymentMethod: 'Cash',
        previousBalance: 200.00,
        newBalance: 80.00
      }
    },
    {
      id: '4',
      type: 'product',
      action: 'Stock Updated',
      description: 'Updated stock quantity for Coca-Cola',
      user: 'Admin',
      timestamp: '2024-01-19T14:20:00Z',
      priority: 'low',
      status: 'success',
      category: 'Inventory Management',
      tags: ['product', 'stock', 'update'],
      details: {
        productName: 'Coca-Cola',
        oldStock: 25,
        newStock: 50,
        reason: 'New delivery received',
        supplier: 'Coca-Cola Bottlers Philippines'
      }
    },
    {
      id: '5',
      type: 'login',
      action: 'User Login',
      description: 'Admin user logged into the system',
      user: 'Admin',
      timestamp: '2024-01-19T08:00:00Z',
      priority: 'low',
      status: 'info',
      category: 'Security',
      tags: ['login', 'security', 'access'],
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      details: {
        ipAddress: '*************',
        location: 'Cebu City, Philippines',
        device: 'Desktop - Windows 10',
        sessionDuration: '4 hours 30 minutes'
      }
    },
    {
      id: '6',
      type: 'system',
      action: 'Backup Created',
      description: 'Automatic database backup completed',
      user: 'System',
      timestamp: '2024-01-19T02:00:00Z',
      priority: 'medium',
      status: 'success',
      category: 'System Maintenance',
      tags: ['backup', 'database', 'maintenance'],
      details: {
        backupSize: '2.5MB',
        backupType: 'Full Backup',
        location: 'Cloud Storage',
        duration: '45 seconds',
        tablesBackedUp: 8
      }
    },
    {
      id: '7',
      type: 'debt',
      action: 'Debt Updated',
      description: 'Updated debt record for Ana Reyes',
      user: 'Admin',
      timestamp: '2024-01-18T15:30:00Z',
      priority: 'high',
      status: 'warning',
      category: 'Customer Management',
      tags: ['debt', 'customer', 'update'],
      details: {
        customer: 'Ana Reyes',
        oldAmount: 75.00,
        newAmount: 100.00,
        reason: 'Additional purchase',
        dueDate: '2024-02-18',
        contactNumber: '09987654321'
      }
    },
    {
      id: '8',
      type: 'product',
      action: 'Product Deleted',
      description: 'Removed "Expired Milk" from product list',
      user: 'Admin',
      timestamp: '2024-01-18T11:10:00Z',
      priority: 'high',
      status: 'error',
      category: 'Inventory Management',
      tags: ['product', 'delete', 'expired'],
      details: {
        productName: 'Expired Milk',
        reason: 'Product expired',
        expiryDate: '2024-01-15',
        quantityRemoved: 12,
        loss: 180.00
      }
    },
    {
      id: '9',
      type: 'security',
      action: 'Failed Login Attempt',
      description: 'Multiple failed login attempts detected',
      user: 'Unknown',
      timestamp: '2024-01-18T03:45:00Z',
      priority: 'critical',
      status: 'error',
      category: 'Security Alert',
      tags: ['security', 'login', 'failed'],
      ipAddress: '*************',
      details: {
        attempts: 5,
        ipAddress: '*************',
        location: 'Unknown',
        blocked: true,
        duration: '24 hours'
      }
    },
    {
      id: '10',
      type: 'notification',
      action: 'Low Stock Alert',
      description: 'Stock level below minimum threshold for multiple products',
      user: 'System',
      timestamp: '2024-01-17T18:00:00Z',
      priority: 'high',
      status: 'warning',
      category: 'Inventory Alert',
      tags: ['stock', 'alert', 'inventory'],
      details: {
        affectedProducts: ['Rice 25kg', 'Cooking Oil 1L', 'Sugar 1kg'],
        minimumThreshold: 10,
        currentStock: [5, 3, 7],
        recommendedOrder: [50, 20, 30]
      }
    },
  ], [])

  // Auto-refresh and connection monitoring
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (autoRefresh) {
      interval = setInterval(() => {
        setLastUpdated(new Date())
        // In a real app, this would fetch new data from the API
        setIsLoading(true)
        setTimeout(() => setIsLoading(false), 1000)
      }, 30000) // Refresh every 30 seconds
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  // Monitor connection status
  useEffect(() => {
    const handleOnline = () => setConnectionStatus('online')
    const handleOffline = () => setConnectionStatus('offline')

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Enhanced utility functions
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <Package className="h-4 w-4" />
      case 'debt':
        return <DollarSign className="h-4 w-4 text-red-500" />
      case 'payment':
        return <DollarSign className="h-4 w-4 text-green-500" />
      case 'login':
        return <User className="h-4 w-4" />
      case 'system':
        return <Database className="h-4 w-4" />
      case 'security':
        return <Shield className="h-4 w-4" />
      case 'backup':
        return <Archive className="h-4 w-4" />
      case 'notification':
        return <Bell className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'high':
        return <Zap className="h-4 w-4 text-orange-500" />
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'low':
        return <Star className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800'
      case 'debt':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800'
      case 'payment':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800'
      case 'login':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800'
      case 'system':
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'
      case 'security':
        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800'
      case 'backup':
        return 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800'
      case 'notification':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
      case 'warning':
        return 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'
      case 'error':
        return 'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
      case 'info':
        return 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800'
      default:
        return 'bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700'
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-700'
    }
  }

  // Advanced filtering and sorting logic
  const filteredHistory = useMemo(() => {
    const filtered = historyData.filter(item => {
      // Search filter
      const matchesSearch = searchTerm === '' ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))

      // Type filter
      const matchesType = filterType === 'all' || item.type === filterType

      // Advanced filters
      const matchesTypes = filters.types.length === 0 || filters.types.includes(item.type)
      const matchesPriorities = filters.priorities.length === 0 || filters.priorities.includes(item.priority)
      const matchesStatuses = filters.statuses.length === 0 || filters.statuses.includes(item.status)
      const matchesUsers = filters.users.length === 0 || filters.users.includes(item.user)
      const matchesCategories = filters.categories.length === 0 || filters.categories.includes(item.category)

      // Date range filter
      const itemDate = new Date(item.timestamp)
      const now = new Date()
      let matchesDateRange = true

      switch (dateRange) {
        case '24hours':
          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 24 * 60 * 60 * 1000
          break
        case '7days':
          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 7 * 24 * 60 * 60 * 1000
          break
        case '30days':
          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 30 * 24 * 60 * 60 * 1000
          break
        case '90days':
          matchesDateRange = (now.getTime() - itemDate.getTime()) <= 90 * 24 * 60 * 60 * 1000
          break
        case 'all':
          matchesDateRange = true
          break
      }

      return matchesSearch && matchesType && matchesTypes && matchesPriorities &&
             matchesStatuses && matchesUsers && matchesCategories && matchesDateRange
    })

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'timestamp':
          comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
        case 'priority':
          const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 }
          comparison = (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) -
                      (priorityOrder[b.priority as keyof typeof priorityOrder] || 0)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [historyData, searchTerm, filterType, filters, dateRange, sortBy, sortOrder])

  // Get unique values for filter options
  const uniquePriorities = [...new Set(historyData.map(item => item.priority))]
  const uniqueStatuses = [...new Set(historyData.map(item => item.status))]
  const uniqueUsers = [...new Set(historyData.map(item => item.user))]

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
    } else {
      return date.toLocaleDateString('en-PH', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  // Bulk operations
  const handleSelectAll = () => {
    if (selectedItems.length === filteredHistory.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredHistory.map(item => item.id))
    }
  }

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const handleBulkDelete = () => {
    if (selectedItems.length > 0) {
      // In a real app, this would make an API call
      // For now, just clear the selection
      setSelectedItems([])
    }
  }

  const handleBulkExport = () => {
    const selectedData = filteredHistory.filter(item => selectedItems.includes(item.id))
    exportHistory(selectedData)
  }

  const exportHistory = (data = filteredHistory) => {
    const csvContent = [
      ['Timestamp', 'Type', 'Action', 'Description', 'User', 'Priority', 'Status', 'Category'],
      ...data.map(item => [
        item.timestamp,
        item.type,
        item.action,
        item.description,
        item.user,
        item.priority,
        item.status,
        item.category
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `revantad-store-history-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  // Activity statistics
  const activityStats = useMemo(() => {
    const stats = {
      total: filteredHistory.length,
      byType: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      byStatus: {} as Record<string, number>,
      byUser: {} as Record<string, number>,
      todayCount: 0,
      weekCount: 0
    }

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    filteredHistory.forEach(item => {
      // Count by type
      stats.byType[item.type] = (stats.byType[item.type] || 0) + 1

      // Count by priority
      stats.byPriority[item.priority] = (stats.byPriority[item.priority] || 0) + 1

      // Count by status
      stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1

      // Count by user
      stats.byUser[item.user] = (stats.byUser[item.user] || 0) + 1

      // Count today and this week
      const itemDate = new Date(item.timestamp)
      if (itemDate >= today) {
        stats.todayCount++
      }
      if (itemDate >= weekAgo) {
        stats.weekCount++
      }
    })

    return stats
  }, [filteredHistory])

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Enhanced Header with Stats */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Activity History
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Comprehensive tracking sa lahat ng activities sa inyong tindahan - makita ang lahat ng nangyari
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-blue-600 dark:text-blue-400">Total Activities</p>
                  <p className="text-lg font-bold text-blue-900 dark:text-blue-300">{activityStats.total}</p>
                </div>
                <BarChart3 className="h-5 w-5 text-blue-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-green-600 dark:text-green-400">Today</p>
                  <p className="text-lg font-bold text-green-900 dark:text-green-300">{activityStats.todayCount}</p>
                </div>
                <TrendingUp className="h-5 w-5 text-green-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-yellow-600 dark:text-yellow-400">This Week</p>
                  <p className="text-lg font-bold text-yellow-900 dark:text-yellow-300">{activityStats.weekCount}</p>
                </div>
                <Clock className="h-5 w-5 text-yellow-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-purple-600 dark:text-purple-400">Selected</p>
                  <p className="text-lg font-bold text-purple-900 dark:text-purple-300">{selectedItems.length}</p>
                </div>
                <CheckSquare className="h-5 w-5 text-purple-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          {/* Connection Status */}
          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium ${
            connectionStatus === 'online'
              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
              : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
          }`}>
            {connectionStatus === 'online' ? (
              <Wifi className="h-4 w-4" />
            ) : (
              <WifiOff className="h-4 w-4" />
            )}
            {connectionStatus === 'online' ? 'Online' : 'Offline'}
          </div>

          {/* Total Activities Count */}
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
            <Hash className="h-4 w-4" />
            {historyData.length} Activities
          </div>

          {/* Auto Refresh Toggle */}
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              autoRefresh
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
            }`}
          >
            <Timer className={`h-4 w-4 ${autoRefresh ? 'animate-pulse' : ''}`} />
            Auto Refresh
          </button>

          {/* Download Options */}
          <div className="relative download-options-container">
            <button
              onClick={() => setShowDownloadOptions(!showDownloadOptions)}
              className="btn-primary flex items-center gap-2 text-sm"
            >
              <Download className="h-4 w-4" />
              Download
              <ChevronDown className={`h-4 w-4 transition-transform ${showDownloadOptions ? 'rotate-180' : ''}`} />
            </button>

            {showDownloadOptions && (
              <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div className="p-4">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                    Download Activity History
                  </h3>

                  {/* Time Range Selection */}
                  <div className="mb-4">
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Select Time Range:
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        { value: 'day', label: 'Last 24 Hours', icon: Clock },
                        { value: 'week', label: 'Last Week', icon: Calendar },
                        { value: 'month', label: 'Last Month', icon: Calendar },
                        { value: 'year', label: 'Last Year', icon: Calendar }
                      ].map(({ value, label, icon: Icon }) => (
                        <button
                          key={value}
                          onClick={() => setDownloadRange(value as any)}
                          className={`flex items-center gap-2 p-2 rounded-lg text-xs transition-colors ${
                            downloadRange === value
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-300 dark:border-green-600'
                              : 'bg-gray-50 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                          }`}
                        >
                          <Icon className="h-3 w-3" />
                          {label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Download Format Buttons */}
                  <div className="space-y-2">
                    <button
                      onClick={() => handleDownload('excel')}
                      className="w-full flex items-center gap-2 p-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors text-sm font-medium"
                    >
                      <Sheet className="h-4 w-4" />
                      Download as Excel ({getFilteredDataByRange(downloadRange).length} records)
                    </button>
                    <button
                      onClick={() => handleDownload('csv')}
                      className="w-full flex items-center gap-2 p-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                    >
                      <FileText className="h-4 w-4" />
                      Download as CSV ({getFilteredDataByRange(downloadRange).length} records)
                    </button>
                    <button
                      onClick={() => handleDownload('json')}
                      className="w-full flex items-center gap-2 p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                    >
                      <Database className="h-4 w-4" />
                      Download as JSON ({getFilteredDataByRange(downloadRange).length} records)
                    </button>
                  </div>

                  {/* Info */}
                  <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      <strong>{getFilteredDataByRange(downloadRange).length}</strong> activities found for the selected time range
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <button
            onClick={fetchRealHistoryData}
            className="btn-outline flex items-center gap-2 text-sm"
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>

          {selectedItems.length > 0 && (
            <>
              <button
                onClick={handleBulkExport}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all"
              >
                <Download className="h-4 w-4" />
                Export Selected
              </button>

              <button
                onClick={handleBulkDelete}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all"
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected
              </button>
            </>
          )}

          <button
            onClick={() => exportHistory()}
            className="btn-primary flex items-center gap-2 text-sm"
          >
            <Download className="h-4 w-4" />
            Export All
          </button>
        </div>
      </div>

      {/* Enhanced Filters */}
      <div className="card p-6 border-l-4 border-l-green-500">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Search className="h-5 w-5 text-green-500" />
            Search & Filters
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors"
            >
              {showFilters ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              Advanced Filters
            </button>

            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              {(['list', 'timeline', 'grid'] as const).map((mode) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode)}
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                    viewMode === mode
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  {mode.charAt(0).toUpperCase() + mode.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search activities, users, descriptions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"
            />
          </div>

          {/* Type Filter */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"
          >
            <option value="all">All Types</option>
            <option value="product">Product Activities</option>
            <option value="debt">Debt Activities</option>
            <option value="payment">Payment Activities</option>
            <option value="login">Login Activities</option>
            <option value="system">System Activities</option>
            <option value="security">Security Activities</option>
            <option value="notification">Notifications</option>
          </select>

          {/* Date Range */}
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"
          >
            <option value="24hours">Last 24 hours</option>
            <option value="7days">Last 7 days</option>
            <option value="30days">Last 30 days</option>
            <option value="90days">Last 90 days</option>
            <option value="all">All time</option>
          </select>

          {/* Sort Options */}
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'type' | 'priority')}
              className="flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"
            >
              <option value="timestamp">Sort by Time</option>
              <option value="type">Sort by Type</option>
              <option value="priority">Sort by Priority</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>

        {/* Advanced Filters (Collapsible) */}
        {showFilters && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Priority Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Priority Levels
                </label>
                <div className="space-y-2">
                  {uniquePriorities.map(priority => (
                    <label key={priority} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.priorities.includes(priority)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              priorities: [...prev.priorities, priority]
                            }))
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              priorities: prev.priorities.filter(p => p !== priority)
                            }))
                          }
                        }}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1">
                        {getPriorityIcon(priority)}
                        {priority}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status Types
                </label>
                <div className="space-y-2">
                  {uniqueStatuses.map(status => (
                    <label key={status} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.statuses.includes(status)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              statuses: [...prev.statuses, status]
                            }))
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              statuses: prev.statuses.filter(s => s !== status)
                            }))
                          }
                        }}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                        {status}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* User Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Users
                </label>
                <div className="space-y-2">
                  {uniqueUsers.map(user => (
                    <label key={user} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.users.includes(user)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              users: [...prev.users, user]
                            }))
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              users: prev.users.filter(u => u !== user)
                            }))
                          }
                        }}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {user}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  setFilters({
                    types: [],
                    priorities: [],
                    statuses: [],
                    dateRange: '7days',
                    users: [],
                    categories: []
                  })
                  setSearchTerm('')
                  setFilterType('all')
                  setDateRange('7days')
                }}
                className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1 transition-colors"
              >
                <X className="h-4 w-4" />
                Clear All Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Activity Stats with Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Activity Types Distribution */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            Activity Distribution
          </h3>
          <div className="space-y-3">
            {Object.entries(activityStats.byType).map(([type, count]) => {
              const percentage = ((count / activityStats.total) * 100).toFixed(1)
              return (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-1.5 rounded-lg ${getTypeColor(type)}`}>
                      {getTypeIcon(type)}
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                      {type}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-900 dark:text-white w-8">
                      {count}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 w-10">
                      {percentage}%
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Priority Analysis */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500" />
            Priority Analysis
          </h3>
          <div className="space-y-3">
            {Object.entries(activityStats.byPriority).map(([priority, count]) => {
              const percentage = ((count / activityStats.total) * 100).toFixed(1)
              return (
                <div key={priority} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getPriorityIcon(priority)}
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                      {priority}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          priority === 'critical' ? 'bg-red-500' :
                          priority === 'high' ? 'bg-orange-500' :
                          priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-900 dark:text-white w-8">
                      {count}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 w-10">
                      {percentage}%
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* User Activity Summary */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <User className="h-5 w-5 text-green-500" />
            User Activity
          </h3>
          <div className="space-y-3">
            {Object.entries(activityStats.byUser).map(([user, count]) => {
              const percentage = ((count / activityStats.total) * 100).toFixed(1)
              return (
                <div key={user} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">
                        {user.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {user}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-bold text-gray-900 dark:text-white w-8">
                      {count}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 w-10">
                      {percentage}%
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Enhanced Activity List */}
      <div className="card overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <FileText className="h-5 w-5 text-green-500" />
                Activity Timeline ({filteredHistory.length})
              </h3>

              {/* Bulk Selection */}
              {filteredHistory.length > 0 && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleSelectAll}
                    className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    {selectedItems.length === filteredHistory.length ? (
                      <CheckSquare className="h-4 w-4 text-green-500" />
                    ) : (
                      <Square className="h-4 w-4" />
                    )}
                    Select All
                  </button>

                  {selectedItems.length > 0 && (
                    <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded-full">
                      {selectedItems.length} selected
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Loading Indicator */}
            {isLoading && (
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <RefreshCw className="h-4 w-4 animate-spin" />
                Loading...
              </div>
            )}
          </div>
        </div>

        <div className="divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto main-content-scroll">
          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center p-12">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Loading activity history...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="flex items-center justify-center p-12">
              <div className="flex flex-col items-center space-y-4 text-center">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Failed to load history</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
                  <button
                    onClick={fetchRealHistoryData}
                    className="mt-3 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Activity List */}
          {!isLoading && !error && filteredHistory.map((item, index) => (
            <div
              key={item.id}
              className={`p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group ${
                selectedItems.includes(item.id) ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500' : ''
              }`}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className="flex items-start space-x-4">
                {/* Selection Checkbox */}
                <button
                  onClick={() => handleSelectItem(item.id)}
                  className="mt-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  {selectedItems.includes(item.id) ? (
                    <CheckSquare className="h-4 w-4 text-green-500" />
                  ) : (
                    <Square className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  )}
                </button>

                {/* Activity Icon */}
                <div className={`p-3 rounded-xl shadow-sm ${getTypeColor(item.type)} transition-all group-hover:scale-105`}>
                  {getTypeIcon(item.type)}
                </div>

                <div className="flex-1 min-w-0">
                  {/* Header */}
                  <div className="flex items-start justify-between gap-4 mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                          {item.action}
                        </h4>

                        {/* Priority Badge */}
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                          {getPriorityIcon(item.priority)}
                          {item.priority}
                        </span>

                        {/* Status Badge */}
                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                        {item.description}
                      </p>

                      {/* Tags */}
                      {item.tags && item.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {item.tags.map(tag => (
                            <span
                              key={tag}
                              className="inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="text-right">
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                        {formatTimestamp(item.timestamp)}
                      </span>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                    <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {item.user}
                      </span>

                      <span className="flex items-center gap-1">
                        <Settings className="h-3 w-3" />
                        {item.category}
                      </span>

                      {item.ipAddress && (
                        <span className="flex items-center gap-1">
                          <Shield className="h-3 w-3" />
                          {item.ipAddress}
                        </span>
                      )}
                    </div>

                    {item.details && (
                      <button
                        onClick={() => setSelectedActivity(item)}
                        className="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 font-medium flex items-center gap-1 transition-colors"
                      >
                        <Eye className="h-3 w-3" />
                        View Details
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Empty State */}
        {filteredHistory.length === 0 && (
          <div className="p-16 text-center">
            <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <Activity className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              Walang Activities na Nakita
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              Try i-adjust ang inyong search terms o filter criteria para makita ang activities na inyong gipangita.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setFilterType('all')
                  setFilters({
                    types: [],
                    priorities: [],
                    statuses: [],
                    dateRange: '7days',
                    users: [],
                    categories: []
                  })
                }}
                className="btn-outline"
              >
                Clear Filters
              </button>
              <button
                onClick={fetchRealHistoryData}
                className="btn-primary"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh Data
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Activity Details Modal */}
      {selectedActivity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-xl ${getTypeColor(selectedActivity.type)}`}>
                    {getTypeIcon(selectedActivity.type)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Activity Details
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {selectedActivity.action}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedActivity(null)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Activity Type
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(selectedActivity.type)}`}>
                        {getTypeIcon(selectedActivity.type)}
                        {selectedActivity.type}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Priority Level
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedActivity.priority)}`}>
                        {getPriorityIcon(selectedActivity.priority)}
                        {selectedActivity.priority}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Status
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedActivity.status)}`}>
                        {selectedActivity.status}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      User
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {selectedActivity.user}
                    </p>
                  </div>

                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Category
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1">
                      <Settings className="h-4 w-4" />
                      {selectedActivity.category}
                    </p>
                  </div>

                  <div>
                    <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Timestamp
                    </label>
                    <p className="text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {new Date(selectedActivity.timestamp).toLocaleString('en-PH')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Description
                </label>
                <p className="text-sm text-gray-900 dark:text-white mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  {selectedActivity.description}
                </p>
              </div>

              {/* Tags */}
              {selectedActivity.tags && selectedActivity.tags.length > 0 && (
                <div>
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Tags
                  </label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {selectedActivity.tags.map(tag => (
                      <span
                        key={tag}
                        className="inline-flex px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-sm rounded-full"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Details */}
              {selectedActivity.details && (
                <div>
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Additional Details
                  </label>
                  <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <pre className="text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono">
                      {JSON.stringify(selectedActivity.details, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {/* Security Information */}
              {(selectedActivity.ipAddress || selectedActivity.userAgent) && (
                <div>
                  <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Security Information
                  </label>
                  <div className="mt-2 space-y-2">
                    {selectedActivity.ipAddress && (
                      <p className="text-sm text-gray-900 dark:text-white flex items-center gap-2">
                        <Shield className="h-4 w-4 text-orange-500" />
                        <span className="font-medium">IP Address:</span>
                        {selectedActivity.ipAddress}
                      </p>
                    )}
                    {selectedActivity.userAgent && (
                      <p className="text-sm text-gray-900 dark:text-white flex items-center gap-2">
                        <Settings className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">User Agent:</span>
                        <span className="truncate">{selectedActivity.userAgent}</span>
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setSelectedActivity(null)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    exportHistory([selectedActivity])
                    setSelectedActivity(null)
                  }}
                  className="btn-primary flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export This Activity
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Real-time Status Footer */}
      <div className="card p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-t-2 border-t-green-500">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className={`w-3 h-3 rounded-full animate-pulse ${
              autoRefresh ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {autoRefresh ? 'Real-time Updates Active' : 'Real-time Updates Paused'}
            </span>
            {isLoading && (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                <span className="text-sm text-blue-600 dark:text-blue-400">Updating...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Last updated: {lastUpdated.toLocaleTimeString('en-PH')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4" />
              <span>Next update: {autoRefresh ? '30s' : 'Manual'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4" />
              <span>Total: {filteredHistory.length} activities</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
